server:
  http:
    addr: 0.0.0.0:8021
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9021
    timeout: 1s
data:
  database:
    driver: mysql
    source: root:root@tcp(127.0.0.1:3306)/test?parseTime=True&loc=Local
  redis:
    addr: 127.0.0.1:6379
    password: ''
    Db: 0
    dial_timeout: 2s
    read_timeout: 2s
    write_timeout: 2s
  mongodb:
    uri: "*************************************"
    timeout: 10s
    db_name: "dynamic_proxy_gateway_dev"
  rabbitmq:
    # addr: amqp://MjpyYWJiaXRtcS1jbi05dDk0NHA0eWowajpMVEFJNXQ4ZlZEeFFRZlp4WnBpVVI4UEQ=:<EMAIL>:5672
    addr: amqp://admin:123456@127.0.0.1:5672
    vhost: /maskfog-dynamic-test
    exchange:
    queue: dev-dynamic-shproxy
    routing_key:
logger:
  type: zap
  zap:
    level: "debug"
    filename: "sh_consumer.log"
    max_size: 500
    max_age: 30
    max_backups: 5
    split_by_level: true
    log_dir: "./logs"
registry:
  type: "consul"
  consul:
    address: "127.0.0.1:8500"
    scheme: "http"
sg_consumer:
  domain: "http://127.0.0.1:8021"
