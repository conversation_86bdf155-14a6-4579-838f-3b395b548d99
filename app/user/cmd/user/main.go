package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/go-kratos/kratos/v2/registry"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"

	"sh_proxy/pkg/bootstrap"
	"sh_proxy/pkg/utils"

	_ "go.uber.org/automaxprocs"
)

var ServiceName = fmt.Sprintf("%s.user.service", utils.GetNamespace())

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()

	Service = bootstrap.NewServiceInfo(
		ServiceName,
		"1.0.0",
		"",
	)
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, rr registry.Registrar) *kratos.App {
	return kratos.New(
		kratos.ID(Service.GetInstanceId()),
		kratos.Name(Service.Name),
		kratos.Version(Service.Version),
		kratos.Metadata(Service.Metadata),
		kratos.Logger(logger),
		kratos.Server(
			gs,
			hs,
		),
		kratos.Registrar(rr),
	)
}

func main() {
	flag.Parse()

	// load configs
	bc := LoadBootstrapConfig(flagconf)
	if bc == nil {
		panic("load config failed")
	}

	// init logger
	logger := bootstrap.NewLoggerProvider(&bootstrap.LoggerConf{
		Type: bootstrap.LoggerType(bc.Logger.Type),
		Zap: &bootstrap.ZapConfig{
			Filename:     bc.Logger.Zap.Filename,
			Level:        bc.Logger.Zap.Level,
			MaxSize:      bc.Logger.Zap.MaxSize,
			MaxAge:       bc.Logger.Zap.MaxAge,
			MaxBackups:   bc.Logger.Zap.MaxBackups,
			SplitByLevel: bc.Logger.Zap.SplitByLevel,
			LogDir:       bc.Logger.Zap.LogDir,
		},
	}, Service)

	var reg registry.Registrar
	// var dis registry.Discovery
	if utils.GetNamespace() != "ipfoxy-local" {
		reg = NewRegistry(bc.Registry)
	}

	app, cleanup, err := wireApp(bc.Server, bc, logger, reg)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
